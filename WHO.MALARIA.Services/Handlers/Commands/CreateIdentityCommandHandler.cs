using MediatR;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Dxos;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules;
using WHO.MALARIA.Services.Rules.Identity;
using WHO.MALARIA.Services.Rules.Shared;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Class to handle create identity command
    /// </summary>
    public class CreateIdentityCommandHandler : RuleBase, IRequestHandler<CreateIdentityCommand, IdentityDto>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IIdentityDxos _identityDxos;
        private readonly IUserDxos _userDxos;
        private readonly IPasswordHasher<Identity> _passwordHasher;
        private readonly IIdentityRuleChecker _identityRuleChecker;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IUserRuleChecker _userRuleChecker;
        private readonly IUserClaimsPrincipalFactory<IdentityDto> _userClaimsPrincipalFactory;
        private readonly ITranslationService _translationService;


        public CreateIdentityCommandHandler(IMediator mediator,
            IUnitOfWork unitOfWork,
            IIdentityDxos identityDxos,
            IUserDxos userDxos,
            IPasswordHasher<Identity> passwordHasher,
            IIdentityRuleChecker identityChecker,
            IUserRuleChecker userRuleChecker,
            ICommonRuleChecker commonRuleChecker,
            IUserClaimsPrincipalFactory<IdentityDto> userClaimsPrincipalFactory, ITranslationService translationService)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            _identityDxos = identityDxos;
            _userDxos = userDxos;
            _passwordHasher = passwordHasher;
            _identityRuleChecker = identityChecker;
            _commonRuleChecker = commonRuleChecker;
            _userRuleChecker = userRuleChecker;
            _userClaimsPrincipalFactory = userClaimsPrincipalFactory;
            _translationService = translationService;
        }

        public async Task<IdentityDto> Handle(CreateIdentityCommand request, CancellationToken cancellationToken)
        {
            // SECURITY FIX: Authorization checks MUST come first to prevent user enumeration attacks
            if (request.UserType == UserRoleEnum.SuperManager || request.UserType == UserRoleEnum.WHOAdmin)
            {
                // Only WHO Admins can create SuperManagers and other WHO Admins
                CheckRule(new UserShouldBeWhoAdminRule(_translationService, _userRuleChecker, request.CurrentUserId));
            }
            else if (request.UserType == UserRoleEnum.Manager)
            {
                // SuperManagers and WHO Admins can create Managers
                CheckRule(new UserShouldBeSuperManagerRule(_translationService, _userRuleChecker, request.CurrentUserId));
            }
            // Viewers can be created by Managers, SuperManagers, or WHO Admins (handled by existing logic)

            // Non-sensitive validation checks (safe to do before sensitive checks)
            CheckRule(new GuidArrayNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.CountryRequestedForIds, "Country"));

            // SECURITY: Sensitive business rules that could leak information - do AFTER authorization
            CheckRule(new ValidEmailRule(_translationService, _identityRuleChecker, request.Email));
            CheckRule(new IdentityEmailMustbeUniqueRule(_translationService, _identityRuleChecker, request.Email));

            if (request.IsRequestCameFromRegisterUserScreen == true)
            {
                CheckRule(new WHOINTUsersNotAllowedToUseRegisterFeatureRule(_translationService, request.Email));
            }

            if (request.UserType == UserRoleEnum.SuperManager)
            {
                CheckRule(new SingleSuperManagerPerCountryRule(_translationService, _userRuleChecker, request.CountryRequestedForIds[0]));
            }

            // create identity
            Identity identity = new Identity(request.Email, request.Email, Constants.IdentityConstant.External, false);
            _unitOfWork.IdentityRepository.Add(identity);

            // create user
            User user = new User(identity.Id, request.Name, request.OrganizationName);
            _unitOfWork.UserRepository.Add(user);

            // User Country Access Mapping
            List<UserCountryAccess> userCountryAccesses = new List<UserCountryAccess>();

            foreach (Guid countryId in request.CountryRequestedForIds)
            {
                UserCountryAccess userCountryAccess = new UserCountryAccess(user.Id, countryId, (int)request.UserType)
                {
                    // status of the country access is approved i.e. 'Accepted' by default for 'SuperManager' user type
                    // and 'Pending' otherwise
                    Status = request.UserType == UserRoleEnum.SuperManager
                            ? (int)UserCountryAccessRightsEnum.Accepted
                            : (int)UserCountryAccessRightsEnum.Pending
                };

                userCountryAccesses.Add(userCountryAccess);
            }

            _unitOfWork.UserCountryAccessRepository.AddRange(userCountryAccesses);
            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            IdentityDto identityDto = _identityDxos.MapIdentityDto(identity);
            identityDto.User = _userDxos.MapUserDto(user);
            identityDto.RequestedCountryIds = request.CountryRequestedForIds;

            _ = _userClaimsPrincipalFactory.CreateAsync(identityDto);

            // send email notification about user and identity creation.
            // await _mediator.Publish(new Domain.Events.IdentityCreatedEvent(identity.Id, "", identity.Email), cancellationToken);

            return identityDto;
        }
    }
}

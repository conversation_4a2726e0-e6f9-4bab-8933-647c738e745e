using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles the ResendInvitationCommand and sends email to the user 
    /// </summary>
    public class ResendInvitationCommandHandler : RuleBase, IRequestHandler<ResendInvitationCommand, bool>
    {
        #region Variable Declaration

        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IUserRuleChecker _userRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IMediator _mediator;
        #endregion

        #region Constructor
        public ResendInvitationCommandHandler(IUnitOfWork unitOfWork,
                                              ICommonRuleChecker commonRuleChecker,
                                              IUserRuleChecker userRuleChecker,
                                              ITranslationService translationService,
                                              IMediator mediator)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _userRuleChecker = userRuleChecker;
            _translationService = translationService;
            _mediator = mediator;
        }
        #endregion

        #region Command Handler

        /// <summary>
        /// Send the invitation email to the user.
        /// </summary>
        public async Task<bool> Handle(ResendInvitationCommand request, CancellationToken cancellationToken)
        {
            //Check business rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.UserId, nameof(request.UserId)));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.CountryId, nameof(request.CountryId)));

            User user = await _unitOfWork.UserRepository.Queryable(u => u.Id == request.UserId)
                                                        .Include(u => u.Identity)
                                                        .Include(u => u.UserCountryAccesses)
                                                        .ThenInclude(u => u.Country)
                                                        .FirstOrDefaultAsync();

            UserCountryAccess countryAccess = user.UserCountryAccesses.Where(uc => uc.CountryId == request.CountryId).FirstOrDefault();


            User currentUser = await _unitOfWork.UserRepository.Queryable(u => u.Id == request.CurrentUserId)
                                                               .Include(u => u.Identity)
                                                               .SingleAsync();

            await _mediator.Publish(new UserInvitationEmailNotification(user.Name, user.Identity.Email, countryAccess.Country.Name, currentUser.Identity.Username, countryAccess.Id));

            return true;
        }
        #endregion
    }
}

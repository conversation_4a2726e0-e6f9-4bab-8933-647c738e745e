using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Command handler to handle update user data using UpdateUserCommand
    /// </summary>
    public class UpdateUserCommandHandler : RuleBase, IRequestHandler<UpdateUserCommand, bool>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IUserRuleChecker _userRuleChecker;
        private readonly ITranslationService _translationService;
        public UpdateUserCommandHandler(IMediator mediator, IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker, IUserRuleChecker userRuleChecker, ITranslationService translationService)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _userRuleChecker = userRuleChecker;
            _translationService = translationService;
        }

        /// <summary>
        /// update user details
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<bool> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.UserId, "UserId"));            
            CheckRule(new CheckForDuplicateCountryRequestRule(_translationService, _userRuleChecker, request.UserId, request.CountryRequestedForIds));

            //get current user
            User user = await _unitOfWork.UserRepository.Queryable(x => x.Id == request.UserId)
                                                        .Include(x => x.UserCountryAccesses)
                                                        .SingleOrDefaultAsync();

            if (user == null)
            {
                throw new RecordNotFoundException(request.UserId, "User");
            }

            //check for new country request
            if (request.CountryRequestedForIds != null && request.CountryRequestedForIds.Any())
            {
                List<UserCountryAccess> userCountryAccesses = new List<UserCountryAccess>();

                foreach (Guid countryId in request.CountryRequestedForIds)
                {
                    //If a user requests for a country that was rejected by the super manager earlier then make its status to pending
                    UserCountryAccess rejectedCountryAccess =  user.UserCountryAccesses.Where(x => x.CountryId == countryId && x.Status == (int)UserCountryAccessRightsEnum.Rejected).SingleOrDefault();
                    if (rejectedCountryAccess != null)
                    {
                        rejectedCountryAccess.Status = (int)UserCountryAccessRightsEnum.Pending;
                        rejectedCountryAccess.RejectionComment = null;
                        _unitOfWork.UserCountryAccessRepository.Update(rejectedCountryAccess);
                        continue;
                    }

                    UserCountryAccess userCountryAccess = new UserCountryAccess(user.Id, countryId, (int)UserRoleEnum.Viewer);
                    userCountryAccesses.Add(userCountryAccess);
                }

                _unitOfWork.UserCountryAccessRepository.AddRange(userCountryAccesses);
            }

            // Update user
            user.Name = request.Name;
            user.OrganizationName = request.OrganizationName;

            _unitOfWork.UserRepository.Update(user);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }
            return true;
        }
    }
}

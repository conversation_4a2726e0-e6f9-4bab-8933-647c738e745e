import { describe, it, expect } from 'vitest';

// Import the SecureExpressionEvaluator class
// Note: We need to extract it from the useFormValidation file for testing
class SecureExpressionEvaluator {
  private pristineFormData: any;
  private isStringNullOrEmpty: (value: string) => boolean;
  private element?: any;

  constructor(
    pristineFormData: any,
    isStringNullOrEmpty: (value: string) => boolean,
    element?: any
  ) {
    this.pristineFormData = pristineFormData;
    this.isStringNullOrEmpty = isStringNullOrEmpty;
    this.element = element;
  }

  evaluate(expression: string): boolean {
    try {
      const normalizedExpression = expression.trim();
      return this.parseExpression(normalizedExpression);
    } catch (error) {
      return false;
    }
  }

  private parseExpression(expr: string): boolean {
    expr = this.resolveParentheses(expr);
    
    if (expr.includes(' || ')) {
      return expr.split(' || ').some(subExpr => this.parseExpression(subExpr.trim()));
    }
    
    if (expr.includes(' && ')) {
      return expr.split(' && ').every(subExpr => this.parseExpression(subExpr.trim()));
    }
    
    if (expr.startsWith('!')) {
      return !this.parseExpression(expr.substring(1).trim());
    }
    
    return this.evaluateComparison(expr);
  }

  private resolveParentheses(expr: string): string {
    while (expr.includes('(')) {
      let start = -1;
      let end = -1;
      
      for (let i = 0; i < expr.length; i++) {
        if (expr[i] === '(') {
          start = i;
        } else if (expr[i] === ')') {
          end = i;
          break;
        }
      }
      
      if (start === -1 || end === -1) break;
      
      const subExpr = expr.substring(start + 1, end);
      const result = this.parseExpression(subExpr);
      expr = expr.substring(0, start) + result.toString() + expr.substring(end + 1);
    }
    
    return expr;
  }

  private evaluateComparison(expr: string): boolean {
    const operators = ['!==', '===', '>=', '<=', '>', '<', '!=', '=='];
    
    for (const op of operators) {
      if (expr.includes(op)) {
        const parts = expr.split(op).map(p => p.trim());
        if (parts.length === 2) {
          const left = this.getValue(parts[0]);
          const right = this.getValue(parts[1]);
          
          switch (op) {
            case '===': return left === right;
            case '!==': return left !== right;
            case '>=': return Number(left) >= Number(right);
            case '<=': return Number(left) <= Number(right);
            case '>': return Number(left) > Number(right);
            case '<': return Number(left) < Number(right);
            case '==': return left == right;
            case '!=': return left != right;
          }
        }
      }
    }
    
    return this.evaluateSimpleExpression(expr);
  }

  private evaluateSimpleExpression(expr: string): boolean {
    if (expr.startsWith('isStringNullOrEmpty(')) {
      const arg = expr.substring(20, expr.length - 1);
      const value = this.getValue(arg);
      return this.isStringNullOrEmpty(String(value));
    }
    
    if (expr.includes('.some(')) {
      return this.evaluateArraySome(expr);
    }
    
    const value = this.getValue(expr);
    return Boolean(value);
  }

  private evaluateArraySome(expr: string): boolean {
    const someMatch = expr.match(/(.+)\.some\((\w+)\s*=>\s*(.+)\)/);
    if (someMatch) {
      const arrayPath = someMatch[1];
      const itemVar = someMatch[2];
      const condition = someMatch[3];
      
      const array = this.getValue(arrayPath);
      if (Array.isArray(array)) {
        return array.some(item => {
          const itemCondition = condition.replace(new RegExp(`\\b${itemVar}\\.`, 'g'), '');
          return this.evaluateItemCondition(item, itemCondition);
        });
      }
    }
    return false;
  }

  private evaluateItemCondition(item: any, condition: string): boolean {
    if (condition.includes(' === ')) {
      const [prop, value] = condition.split(' === ').map(s => s.trim());
      const itemValue = this.getNestedProperty(item, prop);
      const expectedValue = this.parseValue(value);
      return itemValue === expectedValue;
    }
    
    if (condition.includes(' !== ')) {
      const [prop, value] = condition.split(' !== ').map(s => s.trim());
      const itemValue = this.getNestedProperty(item, prop);
      const expectedValue = this.parseValue(value);
      return itemValue !== expectedValue;
    }
    
    return false;
  }

  private getValue(path: string): any {
    path = path.trim();
    
    if (path === 'true') return true;
    if (path === 'false') return false;
    if (path === 'null') return null;
    if (path === 'undefined') return undefined;
    if (/^-?\d+(\.\d+)?$/.test(path)) return Number(path);
    if (path.startsWith('"') && path.endsWith('"')) return path.slice(1, -1);
    if (path.startsWith("'") && path.endsWith("'")) return path.slice(1, -1);
    
    if (path.startsWith('pristineFormData.')) {
      return this.getNestedProperty(this.pristineFormData, path.substring(17));
    }
    
    if (path.startsWith('element.')) {
      return this.getNestedProperty(this.element, path.substring(8));
    }
    
    return undefined;
  }

  private getNestedProperty(obj: any, path: string): any {
    if (!obj || !path) return undefined;
    
    const parts = path.split('.');
    let current = obj;
    
    for (const part of parts) {
      if (part.includes('[') && part.includes(']')) {
        const arrayName = part.substring(0, part.indexOf('['));
        const indexStr = part.substring(part.indexOf('[') + 1, part.indexOf(']'));
        const index = parseInt(indexStr, 10);
        
        current = current?.[arrayName]?.[index];
      } else {
        current = current?.[part];
      }
      
      if (current === undefined || current === null) {
        return current;
      }
    }
    
    return current;
  }

  private parseValue(value: string): any {
    value = value.trim();
    if (value === 'true') return true;
    if (value === 'false') return false;
    if (value === 'null') return null;
    if (value === 'undefined') return undefined;
    if (/^-?\d+(\.\d+)?$/.test(value)) return Number(value);
    if (value.startsWith('"') && value.endsWith('"')) return value.slice(1, -1);
    if (value.startsWith("'") && value.endsWith("'")) return value.slice(1, -1);
    return value;
  }
}

describe('SecureExpressionEvaluator', () => {
  const isStringNullOrEmpty = (value: string) => !value || value.trim().length === 0;

  it('should evaluate boolean equality checks', () => {
    const formData = { cannotBeAssessed: true, canAccessNational: false };
    const evaluator = new SecureExpressionEvaluator(formData, isStringNullOrEmpty);

    expect(evaluator.evaluate('pristineFormData.cannotBeAssessed === true')).toBe(true);
    expect(evaluator.evaluate('pristineFormData.canAccessNational === true')).toBe(false);
    expect(evaluator.evaluate('pristineFormData.canAccessNational === false')).toBe(true);
  });

  it('should evaluate string null/empty checks', () => {
    const formData = { reason: '', name: 'test', missing: null };
    const evaluator = new SecureExpressionEvaluator(formData, isStringNullOrEmpty);

    expect(evaluator.evaluate('isStringNullOrEmpty(pristineFormData.reason)')).toBe(true);
    expect(evaluator.evaluate('isStringNullOrEmpty(pristineFormData.name)')).toBe(false);
    expect(evaluator.evaluate('isStringNullOrEmpty(pristineFormData.missing)')).toBe(true);
  });

  it('should evaluate combined boolean and string checks', () => {
    const formData = { 
      cannotBeAssessed: true, 
      cannotBeAssessedReason: '',
      canAccessNational: true,
      accessUsers: [{ national: 'test' }]
    };
    const evaluator = new SecureExpressionEvaluator(formData, isStringNullOrEmpty);

    expect(evaluator.evaluate('pristineFormData.cannotBeAssessed === true && isStringNullOrEmpty(pristineFormData.cannotBeAssessedReason)')).toBe(true);
    expect(evaluator.evaluate('pristineFormData.canAccessNational === true && isStringNullOrEmpty(pristineFormData.accessUsers[0].national)')).toBe(false);
  });

  it('should evaluate numeric range validations', () => {
    const formData = { nationalData: 50, yearOfData: -5, percentage: 150 };
    const evaluator = new SecureExpressionEvaluator(formData, isStringNullOrEmpty);

    expect(evaluator.evaluate('!(pristineFormData.nationalData >=0 && pristineFormData.nationalData <=100)')).toBe(false);
    expect(evaluator.evaluate('pristineFormData.yearOfData <= 0')).toBe(true);
    expect(evaluator.evaluate('!(pristineFormData.percentage >=0 && pristineFormData.percentage <=100)')).toBe(true);
  });

  it('should evaluate array operations', () => {
    const formData = { 
      malariaIndicators: [
        { indicatorMonitored: null },
        { indicatorMonitored: 'test' }
      ],
      checkListIndicatorsCount: 3
    };
    const evaluator = new SecureExpressionEvaluator(formData, isStringNullOrEmpty);

    expect(evaluator.evaluate('pristineFormData.malariaIndicators.some(data => data.indicatorMonitored === null)')).toBe(true);
    expect(evaluator.evaluate('pristineFormData.checkListIndicatorsCount !== pristineFormData.malariaIndicators.length')).toBe(true);
  });

  it('should evaluate complex logical combinations', () => {
    const formData = { 
      malariaIndicators: [{ indicatorMonitored: null }],
      checkListIndicatorsCount: 2
    };
    const evaluator = new SecureExpressionEvaluator(formData, isStringNullOrEmpty);

    const complexExpression = '(pristineFormData.malariaIndicators.some(data => data.indicatorMonitored === null) || (pristineFormData.checkListIndicatorsCount !== pristineFormData.malariaIndicators.length))';
    expect(evaluator.evaluate(complexExpression)).toBe(true);
  });

  it('should handle element-based evaluations for key-value pairs', () => {
    const formData = {};
    const element = { key: 'testKey', value: '' };
    const evaluator = new SecureExpressionEvaluator(formData, isStringNullOrEmpty, element);

    expect(evaluator.evaluate('isStringNullOrEmpty(element.value)')).toBe(true);
    expect(evaluator.evaluate('isStringNullOrEmpty(element.key)')).toBe(false);
  });

  it('should safely handle invalid expressions', () => {
    const formData = {};
    const evaluator = new SecureExpressionEvaluator(formData, isStringNullOrEmpty);

    expect(evaluator.evaluate('invalid.expression.that.does.not.exist')).toBe(false);
    expect(evaluator.evaluate('malformed === expression ===')).toBe(false);
    expect(evaluator.evaluate('')).toBe(false);
  });
});

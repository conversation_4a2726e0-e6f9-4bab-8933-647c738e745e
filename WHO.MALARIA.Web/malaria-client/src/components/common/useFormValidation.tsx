import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { Constants } from "../../models/Constants";
import { KeyValuePair } from "../../models/DeskReview/KeyValueType";
import { DataType, ValidationAttribute } from "../../models/Enums";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../models/ValidationRuleModel";
import { setError, removeErrorProperty } from "../../redux/ducks/error";

/**
 * Secure expression evaluator that replaces the vulnerable Function constructor
 * Supports safe evaluation of validation expressions without code injection risks
 */
class SecureExpressionEvaluator {
  private pristineFormData: any;
  private isStringNullOrEmpty: (value: string) => boolean;
  private element?: any; // For key-value pair evaluations

  constructor(
    pristineFormData: any,
    isStringNullOrEmpty: (value: string) => boolean,
    element?: any
  ) {
    this.pristineFormData = pristineFormData;
    this.isStringNullOrEmpty = isStringNullOrEmpty;
    this.element = element;
  }

  /**
   * Safely evaluates a validation expression without using Function constructor
   * @param expression The validation expression to evaluate
   * @returns boolean result of the expression evaluation
   */
  evaluate(expression: string): boolean {
    try {
      // Remove whitespace and normalize the expression
      const normalizedExpression = expression.trim();

      // Parse and evaluate the expression using a secure approach
      return this.parseExpression(normalizedExpression);
    } catch (error) {
      // Use console.warn only in development
      if (process.env.NODE_ENV === "development") {
        console.warn("Expression evaluation failed:", error);
      }
      return false;
    }
  }

  private parseExpression(expr: string): boolean {
    // Handle parentheses by recursively evaluating sub-expressions
    expr = this.resolveParentheses(expr);

    // Handle logical operators (OR has lower precedence than AND)
    if (expr.includes(" || ")) {
      return expr
        .split(" || ")
        .some(subExpr => this.parseExpression(subExpr.trim()));
    }

    if (expr.includes(" && ")) {
      return expr
        .split(" && ")
        .every(subExpr => this.parseExpression(subExpr.trim()));
    }

    // Handle negation
    if (expr.startsWith("!")) {
      return !this.parseExpression(expr.substring(1).trim());
    }

    // Handle comparison operations
    return this.evaluateComparison(expr);
  }

  private resolveParentheses(expr: string): string {
    while (expr.includes("(")) {
      // Find the innermost parentheses
      let start = -1;
      let end = -1;

      for (let i = 0; i < expr.length; i++) {
        if (expr[i] === "(") {
          start = i;
        } else if (expr[i] === ")") {
          end = i;
          break;
        }
      }

      if (start === -1 || end === -1) break;

      const subExpr = expr.substring(start + 1, end);
      const result = this.parseExpression(subExpr);
      expr =
        expr.substring(0, start) + result.toString() + expr.substring(end + 1);
    }

    return expr;
  }

  private evaluateComparison(expr: string): boolean {
    // Handle different comparison operators
    const operators = ["!==", "===", ">=", "<=", ">", "<", "!=", "=="];

    for (const op of operators) {
      if (expr.includes(op)) {
        const parts = expr.split(op).map(p => p.trim());
        if (parts.length === 2) {
          const left = this.getValue(parts[0]);
          const right = this.getValue(parts[1]);

          switch (op) {
            case "===":
              return left === right;
            case "!==":
              return left !== right;
            case ">=":
              return Number(left) >= Number(right);
            case "<=":
              return Number(left) <= Number(right);
            case ">":
              return Number(left) > Number(right);
            case "<":
              return Number(left) < Number(right);
            case "==":
              return left == right;
            case "!=":
              return left != right;
          }
        }
      }
    }

    // Handle function calls and simple boolean values
    return this.evaluateSimpleExpression(expr);
  }

  private evaluateSimpleExpression(expr: string): boolean {
    // Handle function calls
    if (expr.startsWith("isStringNullOrEmpty(")) {
      const arg = expr.substring(20, expr.length - 1); // Remove 'isStringNullOrEmpty(' and ')'
      const value = this.getValue(arg);
      return this.isStringNullOrEmpty(String(value));
    }

    // Handle array.some() calls
    if (expr.includes(".some(")) {
      return this.evaluateArraySome(expr);
    }

    // Handle simple boolean values or property access
    const value = this.getValue(expr);
    return Boolean(value);
  }

  private evaluateArraySome(expr: string): boolean {
    // Parse expressions like "pristineFormData.malariaIndicators.some(data => data.indicatorMonitored === null)"
    const someMatch = expr.match(/(.+)\.some\((\w+)\s*=>\s*(.+)\)/);
    if (someMatch) {
      const arrayPath = someMatch[1];
      const itemVar = someMatch[2];
      const condition = someMatch[3];

      const array = this.getValue(arrayPath);
      if (Array.isArray(array)) {
        return array.some(item => {
          // Replace the item variable in the condition with actual values
          const itemCondition = condition.replace(
            new RegExp(`\\b${itemVar}\\.`, "g"),
            ""
          );
          return this.evaluateItemCondition(item, itemCondition);
        });
      }
    }
    return false;
  }

  private evaluateItemCondition(item: any, condition: string): boolean {
    // Handle simple property comparisons within array.some()
    if (condition.includes(" === ")) {
      const [prop, value] = condition.split(" === ").map(s => s.trim());
      const itemValue = this.getNestedProperty(item, prop);
      const expectedValue = this.parseValue(value);
      return itemValue === expectedValue;
    }

    if (condition.includes(" !== ")) {
      const [prop, value] = condition.split(" !== ").map(s => s.trim());
      const itemValue = this.getNestedProperty(item, prop);
      const expectedValue = this.parseValue(value);
      return itemValue !== expectedValue;
    }

    return false;
  }

  private getValue(path: string): any {
    path = path.trim();

    // Handle literal values
    if (path === "true") return true;
    if (path === "false") return false;
    if (path === "null") return null;
    if (path === "undefined") return undefined;
    if (/^-?\d+(\.\d+)?$/.test(path)) return Number(path);
    if (path.startsWith('"') && path.endsWith('"')) return path.slice(1, -1);
    if (path.startsWith("'") && path.endsWith("'")) return path.slice(1, -1);

    // Handle property access
    if (path.startsWith("pristineFormData.")) {
      return this.getNestedProperty(this.pristineFormData, path.substring(17));
    }

    if (path.startsWith("element.")) {
      return this.getNestedProperty(this.element, path.substring(8));
    }

    // Handle mathematical expressions
    if (this.isMathExpression(path)) {
      return this.evaluateMathExpression(path);
    }

    return undefined;
  }

  private getNestedProperty(obj: any, path: string): any {
    if (!obj || !path) return undefined;

    const parts = path.split(".");
    let current = obj;

    for (const part of parts) {
      if (part.includes("[") && part.includes("]")) {
        // Handle array access like "accessUsers[0]"
        const arrayName = part.substring(0, part.indexOf("["));
        const indexStr = part.substring(
          part.indexOf("[") + 1,
          part.indexOf("]")
        );
        const index = parseInt(indexStr, 10);

        current = current?.[arrayName]?.[index];
      } else {
        current = current?.[part];
      }

      if (current === undefined || current === null) {
        return current;
      }
    }

    return current;
  }

  private parseValue(value: string): any {
    value = value.trim();
    if (value === "true") return true;
    if (value === "false") return false;
    if (value === "null") return null;
    if (value === "undefined") return undefined;
    if (/^-?\d+(\.\d+)?$/.test(value)) return Number(value);
    if (value.startsWith('"') && value.endsWith('"')) return value.slice(1, -1);
    if (value.startsWith("'") && value.endsWith("'")) return value.slice(1, -1);
    return value;
  }

  private isMathExpression(expr: string): boolean {
    return /[\+\-\*\/]/.test(expr) && expr.includes("pristineFormData");
  }

  private evaluateMathExpression(expr: string): number {
    // Handle simple mathematical expressions safely without Function constructor
    try {
      // Replace property access with actual values
      let processedExpr = expr;
      const propertyMatches = expr.match(
        /pristineFormData\.[a-zA-Z0-9_\.\[\]]+/g
      );

      if (propertyMatches) {
        for (const match of propertyMatches) {
          const value = this.getValue(match);
          processedExpr = processedExpr.replace(
            match,
            String(Number(value) || 0)
          );
        }
      }

      // Only allow basic math operations - no function calls or complex expressions
      if (/^[\d\s\+\-\*\/\(\)\.]+$/.test(processedExpr)) {
        return this.evaluateArithmeticExpression(processedExpr);
      }
    } catch (error) {
      // Use console.warn only in development
      if (process.env.NODE_ENV === "development") {
        console.warn("Math expression evaluation failed:", error);
      }
    }

    return 0;
  }

  private evaluateArithmeticExpression(expr: string): number {
    // Simple arithmetic expression evaluator without using Function constructor
    // This handles basic operations: +, -, *, /, parentheses
    try {
      // Remove all whitespace
      expr = expr.replace(/\s/g, "");

      // Handle parentheses first
      while (expr.includes("(")) {
        const innerMatch = expr.match(/\([^()]+\)/);
        if (!innerMatch) break;

        const innerExpr = innerMatch[0].slice(1, -1); // Remove parentheses
        const result = this.evaluateSimpleArithmetic(innerExpr);
        expr = expr.replace(innerMatch[0], result.toString());
      }

      return this.evaluateSimpleArithmetic(expr);
    } catch (error) {
      return 0;
    }
  }

  private evaluateSimpleArithmetic(expr: string): number {
    // Handle multiplication and division first (higher precedence)
    let tokens = this.tokenizeArithmetic(expr);

    // Process * and / from left to right
    for (let i = 1; i < tokens.length - 1; i += 2) {
      if (tokens[i] === "*") {
        const result = Number(tokens[i - 1]) * Number(tokens[i + 1]);
        tokens.splice(i - 1, 3, result.toString());
        i -= 2;
      } else if (tokens[i] === "/") {
        const divisor = Number(tokens[i + 1]);
        if (divisor === 0) return 0; // Avoid division by zero
        const result = Number(tokens[i - 1]) / divisor;
        tokens.splice(i - 1, 3, result.toString());
        i -= 2;
      }
    }

    // Process + and - from left to right
    for (let i = 1; i < tokens.length - 1; i += 2) {
      if (tokens[i] === "+") {
        const result = Number(tokens[i - 1]) + Number(tokens[i + 1]);
        tokens.splice(i - 1, 3, result.toString());
        i -= 2;
      } else if (tokens[i] === "-") {
        const result = Number(tokens[i - 1]) - Number(tokens[i + 1]);
        tokens.splice(i - 1, 3, result.toString());
        i -= 2;
      }
    }

    return Number(tokens[0]) || 0;
  }

  private tokenizeArithmetic(expr: string): string[] {
    // Split expression into numbers and operators
    const tokens: string[] = [];
    let currentNumber = "";

    for (let i = 0; i < expr.length; i++) {
      const char = expr[i];

      if (/[\+\-\*\/]/.test(char)) {
        if (currentNumber) {
          tokens.push(currentNumber);
          currentNumber = "";
        }
        tokens.push(char);
      } else if (/[\d\.]/.test(char)) {
        currentNumber += char;
      }
    }

    if (currentNumber) {
      tokens.push(currentNumber);
    }

    return tokens;
  }
}

/**
 * Provides form validation method to validate the form data
 * @param validationRules This is configuration of the rules against which the validation will happen
 * @returns validate function
 */
const useFormValidation = (validationRules: IValidationRuleProvider) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  //Checks if input string is null or undefined or empty and returns true or false
  const isStringNullOrEmpty = (value: string) =>
    !value || value?.trim().length === 0;

  //Checks if key or value is null or undefined or empty for the given key value pair array
  const checkForEmptyKeyValuePair = (
    keyValuePairs: Array<KeyValuePair<any, any>>
  ) => {
    let errorInKeyValuePair: Array<KeyValuePair<any, any>> = [];

    keyValuePairs.forEach((element: any) => {
      let key;
      let value;
      if (isStringNullOrEmpty(element.key?.toString())) {
        key = t("Errors.MandatoryField");
      }

      if (isStringNullOrEmpty(element.value?.toString())) {
        value = t("Errors.MandatoryField");
      }

      errorInKeyValuePair = [...errorInKeyValuePair, { key, value }];
    });

    return errorInKeyValuePair;
  };

  let pristineFormData: any;
  let isFormValid: boolean = true;

  /**
   * Perform form validation on the input
   * @param formData Data on which the validation rules will be validated.
   * @param parentObjectKey Holds the root object's key name, this is mostly useful while calling this function recursively
   */
  const validate = (formData: any, parentObjectKey: string = "") => {
    if (!pristineFormData) {
      pristineFormData = formData;
    }

    //Validate Array object
    if (Array.isArray(formData)) {
      const validationRule: ValidationRuleModel =
        validationRules[parentObjectKey];

      if (validationRule) {
        switch (validationRule.dataType) {
          case DataType.ArrayOfKeyValuePair:
            validateKeyValuePairArray(formData, parentObjectKey);
            break;
          case DataType.ArrayOfObject:
            formData.forEach((data, index: number) => {
              validate(data, `${parentObjectKey}[${index}]`);
            });
            break;
        }
      } else {
        formData.forEach(data => {
          validate(data, parentObjectKey);
        });
      }
    } else {
      for (const dataKey in formData) {
        /**Generate the full path for the field which will be validated based on the rule is defined in the ValidationRules object
                          for Ex. The field reside in the object as follow and when we validate for innerField then the full key path would be 
                          "step_a.field1.innerField1" and the same must be there in the ValidationRules object.
                        /**
                         * {
                         *  "step_a":{
                         *      field1:{
                         *        innerField:""
                         *     },
                         *   }
                         * }
                         */

        const fullKeyPath = parentObjectKey
          ? `${parentObjectKey}.${dataKey}`
          : dataKey;

        /**
         * If validation is going to perform on the array object then validation rule must have the [{index}], and to check if
         * the rule exists we have to replace the [0] with [{index}].
         *
         * for ex. validation rules has following rule "array_field[{index}].childField: { dataType:string, required: true}" and
         * fullKeyPath has following value "array_field[0].childField", so to get the rule for childField we have to replace the
         * [0] with [{index}] so that it will match the key and return the rule.
         */
        const matchArraySignaturePattern = /\[[0-9]+\]/;
        const hasArraySignature = new RegExp(matchArraySignaturePattern).test(
          fullKeyPath
        );

        const validationRule: ValidationRuleModel = hasArraySignature
          ? validationRules[
              fullKeyPath.replace(
                matchArraySignaturePattern,
                `[${Constants.Common.IndexSubstitute}]`
              )
            ]
          : validationRules[fullKeyPath];

        if (validationRule) {
          //If object is null or array object is empty then set error
          if (
            (formData[dataKey] === null || formData[dataKey]?.length === 0) &&
            validationRule.required === true
          ) {
            dispatch(
              setError({
                [fullKeyPath]: t("Errors.MandatoryField"),
              })
            );
            isFormValid = false;
            continue;
          } else {
            dispatch(removeErrorProperty(fullKeyPath));
          }

          //If property of formData is an object or an array type then call the same function recursively and perform the validation for its children
          if (
            validationRule.dataType === DataType.Object ||
            validationRule.dataType === DataType.ArrayOfKeyValuePair ||
            validationRule.dataType === DataType.ArrayOfObject
          ) {
            validate(formData[dataKey], fullKeyPath);
          }
          //validate the non object properties such as string, number etc.
          else {
            for (const ruleKey in validationRule) {
              switch (ruleKey) {
                case ValidationAttribute.Required:
                  if (validationRule.required === true) {
                    switch (validationRule.dataType) {
                      case DataType.String:
                        if (isStringNullOrEmpty(formData[dataKey])) {
                          dispatch(
                            setError({
                              [fullKeyPath]: t("Errors.MandatoryField"),
                            })
                          );
                          isFormValid = false;
                        } else {
                          dispatch(removeErrorProperty(fullKeyPath));
                        }
                        break;
                      case DataType.Boolean:
                        if (
                          formData[dataKey] === true ||
                          formData[dataKey] === false
                        ) {
                          dispatch(removeErrorProperty(fullKeyPath));
                        } else {
                          dispatch(
                            setError({
                              [fullKeyPath]: t("Errors.MandatoryField"),
                            })
                          );
                          isFormValid = false;
                        }
                        break;
                      case DataType.Number:
                        if (
                          [null, undefined].includes(formData[dataKey]) ||
                          isNaN(formData[dataKey])
                        ) {
                          dispatch(
                            setError({
                              [fullKeyPath]: t("Errors.MandatoryField"),
                            })
                          );
                          isFormValid = false;
                        } else {
                          dispatch(removeErrorProperty(fullKeyPath));
                        }
                        break;
                      default:
                        break;
                    }
                  }
                  break;
                case ValidationAttribute.Condition:
                  if (validationRule.condition) {
                    //pristineFormData - this name must be same as the object name that gets assigned at the start of this function by the formData input param
                    let predicate: string = validationRule.condition.replaceAll(
                      Constants.Common.RootObjectNameSubstitute,
                      "pristineFormData"
                    );

                    /**
                                                             * If condition has fields which are coming from array then the predicate will have array signature
                                                                as [{index}], to replace this with an actual index we are matching [{index}] using regex and 
                                                                replacing it with an index we have in fullKeyPath variable.
                                                                
                                                                for ex. condition is "array_object[{index}].numberField_1 > array_object[{index}].numberField_2"
                                                                and fullKeyPath has following string "array_object[0].numberField_1 > array_object[0].numberField_2".
                    
                                                                Inside the below If condition we are matching the index value that is zero in this example and
                                                                replacing it from the predicate.
                                                             */
                    if (hasArraySignature) {
                      const indexMatch = new RegExp(/(?!=\[)[0-9]+(?=\])/).exec(
                        fullKeyPath
                      );
                      predicate = predicate.replace(
                        new RegExp(`${Constants.Common.IndexSubstitute}`, "g"),
                        indexMatch ? indexMatch[0] : ""
                      );
                    }

                    // Only allow expressions in predicate, not statements or declarations
                    if (/\b(const|let|var)\b/.test(predicate)) {
                      // Skip condition check and remove error if predicate is invalid
                      dispatch(removeErrorProperty(fullKeyPath));
                      break;
                    }

                    // Use secure expression evaluator instead of Function constructor
                    const evaluator = new SecureExpressionEvaluator(
                      pristineFormData,
                      isStringNullOrEmpty
                    );

                    if (evaluator.evaluate(predicate) === true) {
                      dispatch(
                        setError({
                          [fullKeyPath]: validationRule.errorMessage
                            ? t(validationRule.errorMessage)
                            : t("Errors.MandatoryField"),
                        })
                      );
                      isFormValid = false;
                    } else {
                      dispatch(removeErrorProperty(fullKeyPath));
                    }
                  }
                  break;
                default:
                  break;
              }
            }
          }
        }
      }
    }

    return isFormValid;
  };

  //Evaulate the condition specified for key and value
  const evaluateConditionForKeyValuePair = (
    keyValuePairs: Array<KeyValuePair<any, any>>,
    keyFieldCondition: string,
    valueFieldCondition: string,
    keyFieldErrorMessage: string,
    valueFieldErrorMessage: string
  ) => {
    let errorInKeyValuePair: Array<KeyValuePair<any, any>> = [];

    keyValuePairs.forEach((element: any) => {
      let key;
      let value;

      //The "element" word used to replace the key and value substitute has to be same as (element: any) param name of the
      //enclosing forEach loop, because at run time the condition executes on the element of the keyValuePairs.
      const keyPredicate: string = keyFieldCondition?.replaceAll(
        Constants.Common.KeySubstitute,
        "element.key"
      );
      const valuePredicate: string = valueFieldCondition?.replaceAll(
        Constants.Common.ValueSubstitute,
        "element.value"
      );

      // Use secure expression evaluator instead of Function constructor
      let keyConditionResult = false;
      let valueConditionResult = false;
      if (keyPredicate) {
        try {
          const keyEvaluator = new SecureExpressionEvaluator(
            pristineFormData,
            isStringNullOrEmpty,
            element
          );
          keyConditionResult = keyEvaluator.evaluate(keyPredicate);
        } catch (e) {
          keyConditionResult = false;
        }
      }
      if (valuePredicate) {
        try {
          const valueEvaluator = new SecureExpressionEvaluator(
            pristineFormData,
            isStringNullOrEmpty,
            element
          );
          valueConditionResult = valueEvaluator.evaluate(valuePredicate);
        } catch (e) {
          valueConditionResult = false;
        }
      }
      if (keyConditionResult === true) {
        key = t(keyFieldErrorMessage);
      }
      if (valueConditionResult === true) {
        value = t(valueFieldErrorMessage);
      }
      errorInKeyValuePair = [...errorInKeyValuePair, { key, value }];
    });

    return errorInKeyValuePair;
  };

  //Validate required and conditional validation on array of key value pair
  const validateKeyValuePairArray = (
    formData: Array<KeyValuePair<any, any>>,
    parentObjectKey: string
  ) => {
    let errorInKeyValuePair: Array<KeyValuePair<any, any>> = new Array<
      KeyValuePair<any, any>
    >();

    const keyFieldValidationRule: ValidationRuleModel =
      validationRules[`${parentObjectKey}.key`];
    const valueFieldValidationRule: ValidationRuleModel =
      validationRules[`${parentObjectKey}.value`];

    if (
      keyFieldValidationRule?.required ||
      valueFieldValidationRule?.required
    ) {
      errorInKeyValuePair = checkForEmptyKeyValuePair(formData);
    }

    //If errorInKeyValuePair is not having any error for required validation and condition is specified for either key or value
    if (
      errorInKeyValuePair.every(
        (kvp: KeyValuePair<any, any>) => !kvp.key && !kvp.value
      ) &&
      (keyFieldValidationRule?.condition || valueFieldValidationRule?.condition)
    ) {
      errorInKeyValuePair = evaluateConditionForKeyValuePair(
        formData,
        keyFieldValidationRule?.condition,
        valueFieldValidationRule?.condition,
        keyFieldValidationRule?.errorMessage,
        valueFieldValidationRule?.errorMessage
      );
    }

    if (
      errorInKeyValuePair.length > 0 &&
      errorInKeyValuePair.some(
        (kvp: KeyValuePair<any, any>) => kvp.key || kvp.value
      )
    ) {
      dispatch(
        setError({
          [parentObjectKey]: errorInKeyValuePair,
        })
      );
      isFormValid = false;
    } else {
      dispatch(removeErrorProperty(parentObjectKey));
    }
  };

  return validate;
};

export default useFormValidation;
